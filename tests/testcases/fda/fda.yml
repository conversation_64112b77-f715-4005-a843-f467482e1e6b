- config:
    name: 通过文件创建签署流程
    base_url: ${ENV(base_url)}
    variables:
      - env: ${ENV(env)}
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - file_id: ${ENV(file_id_in_app_id_tengqing_SaaS_with_willing)}
      - file_2: ${ENV(file_2_in_app_id_tengqing_SaaS_with_willing)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id1_in_tsign)}
      - account_id_2_tsign: ${ENV(account_id2_in_tsign)}
      - account_id_3_tsign: ${ENV(account_id3_in_tsign)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - org_id_1: ${ENV(orgid_dy)}
      - org_id_2: ${ENV(orgid_sx)}
      - phone_1: ${ENV(phone_1)}
      - phone_2: ${ENV(phone_2)}
      - phone_3: ${ENV(phone_3)}

- test:
    name: "signType非枚举值"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "fdb" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "$account_id_1_tsign" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, signType无对应枚举值 ]

- test:
    name: "个人无法发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "signFlowInitiator": { "psnInitiator": { "psnId": "${account_id_1_tsign}" } }, "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "$account_id_2_tsign" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1437617 ]
      - contains: [ content.message, 个人发起方不允许发起FDA流程 ]

- test:
    name: "signType为FDAType"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": false,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,2","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 0 ]
    extract:
      - flowId: content.data.signFlowId

- test:
    name: "FDA流程不能追加签署文件"
    api: api/sign-flow/unsigned-files-add.yml
    variables:
      flowId: $flowId
      json: { "unsignedFiles": [ { "fileEditPwd": "","fileId": "${file_2}","fileName": "追加的文件7","neededPwd": false } ] }
    validate:
      - eq: [ content.code, 1437529 ]
      - contains: [ content.message, FDA签署流程不支持添加待签文件 ]

- test:
    name: "分布无法仅追加企业签署方"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: { "signers": [ { "noticeConfig": { "noticeTypes": "1,2" },"orgSignerInfo": { "orgId": "${account_id_sx_tsign}","transactorInfo": { "psnId": "${account_id_2_tsign}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "fileId": "${file_id}","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 100,"positionY": 100 },"signFieldSize": 20,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "","fontSize": 0,"showSignDate": 0,"signDatePositionX": 0,"signDatePositionY": 0 },"signFieldType": 0 } ],"signerType": 1 } ] }
    validate:
      - eq: [ content.code,  1435002 ]
      - contains: [ content.message, "FDA签名要求每个签署方至少包含一个经办人章或个人章" ]

- test:
    name: "可以追加企业签署方+经办人签署"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: { "signers": [ { "noticeConfig": { "noticeTypes": "1,2" },"orgSignerInfo": { "orgId": "${account_id_dy_tsign}","transactorInfo": { "psnId": "${account_id_2_tsign}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "fileId": "${file_id}","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 100,"positionY": 100 },"signFieldSize": 20,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "","fontSize": 0,"showSignDate": 0,"signDatePositionX": 0,"signDatePositionY": 0 },"signFieldType": 0 } ],"signerType": 1 }, { "noticeConfig": { "noticeTypes": "1,2" },"orgSignerInfo": { "orgId": "${account_id_dy_tsign}","transactorInfo": { "psnId": "${account_id_2_tsign}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "fileId": "${file_id}","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 100,"positionY": 100 },"signFieldSize": 20,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "","fontSize": 0,"showSignDate": 0,"signDatePositionX": 0,"signDatePositionY": 0 },"signFieldType": 0 } ],"signerType": 3 } ] }
    validate:
      - eq: [ content.code,  0 ]
      - contains: [ content.message, "成功" ]
    extract:
      - signFieldId1: content.data.0.signFieldId
      - signFieldId2: content.data.1.signFieldId


- test:
    name: "删除经办人签署区-失败"
    api: api/sign-flow/sign-fields-delete.yml
    variables:
      flowId: $flowId
      signFieldIds: $signFieldId2
    validate:
      - eq: [ content.code,  1435002 ]
      - contains: [ content.message, "FDA签名要求每个签署方至少包含一个经办人章或个人章" ]

- test:
    name: "删除企业签署区-成功"
    api: api/sign-flow/sign-fields-delete.yml
    variables:
      flowId: $flowId
      signFieldIds: $signFieldId1
    validate:
      - eq: [ content.code, 0 ]
      - eq: [ content.message, 成功 ]

- test:
    name: "再次删除经办人签署区-失败"
    api: api/sign-flow/sign-fields-delete.yml
    variables:
      flowId: $flowId
      signFieldIds: $signFieldId2
    validate:
      - eq: [ content.code,  1435002 ]
      - contains: [ content.message, "FDA签名要求每个签署方至少包含一个经办人章或个人章" ]

- test:
    name: "添加平台自动签失败"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: { "signers": [ { "noticeConfig": { "noticeTypes": "1,2" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "fileId": "${file_id}","normalSignFieldConfig": { "assignedSealId": "","autoSign": true,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 100,"positionY": 100 },"signFieldSize": 20,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "","fontSize": 0,"showSignDate": 0,"signDatePositionX": 0,"signDatePositionY": 0 },"signFieldType": 0 } ],"signerType": 1 } ] }
    validate:
      - eq: [ content.code,  1435002 ]
      - contains: [ content.message, "FDA签名流程，不允许存在自动签署区" ]

- test:
    name: "开启流程"
    api: api/sign-flow/start.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [ content.code, 0 ]

- test:
    name: 获取批量签署url
    variables:
      - json: {
        "operatorId": $account_id_2_tsign,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId
        ]
      }
    api: api/batchSignV3/batchSignUrl.yml
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data",0 ]
    extract:
      - batchSerialId: content.data.batchSerialId

- test:
    name: 获取批量签署流程列表-批量签不支持启用FDA签名流程
    variables:
      - batchSerialId: $batchSerialId
    api: api/batchSignV3/getBatchSignList.yml
    validate:
      - eq: [ status_code, 200 ]
      - len_eq: [ "content.data.assignedPosSealSigns", 0 ]  #可签署为0
      - len_eq: [ "content.data.noSupportSigns", 1 ]  #不可签为1
      - contains: [ "content.data.noSupportSigns.0.reason", 批量签不支持启用FDA签名流程 ]  #批量签不支持启用FDA签名流程

#- test:
#    name: 签署原FDA流程
#    variables:
#      - json: { "env": "${env}", "flowId": "${flowId}","group": "${group}" }
#    api: api/utils/demosdk/sign/start.yml
#    validate:
#      - eq: [ status_code, 200 ]
#      - eq: [ "content.success",True ]

#- test:
#    name: 签署原FDA流程
#    variables:
#      - params: env=${env}&flow_id=${flowId}&group=${group}
#    api: api/utils/mcp/signing/one_click_sign.yml
#    teardown_hooks:
#      - ${hook_sleep_n_secs(3)}

#- test:
#    name: 可以发起解约
#    variables:
#      - flowId: ${flowId}
#      - json: { "rescindFileList": [ "${file_id}" ],"rescindReason": 2,"rescindReasonNotes": "333","rescissionInitiator": { "psnInitiator": { "psnId": "${account_id_2_tsign}" } },"signFlowConfig": { "noticeConfig": { "examineNotice": true,"noticeTypes": "1" },"signConfig": { "availableSignClientTypes": "2" } } }
#    api: api/v3/sign-flow/signFlowId/initiate-rescission.yml
#    validate:
#      - eq: [ status_code, 200 ]
#      - eq: [ "content.code",0 ]
#      - contains: [ "content.message",成功 ]

- test:
    name: "fdaLanguageMode非枚举值"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaLanguageMode": "JAPANESE" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - ne: [ content.code, 0 ]


- test:
    name: "fdaLanguageMode为英文时传AI手绘"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaLanguageMode": "ENGLISH" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "2","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    extract:
      - message: content.message
    validate:
      - eq: [ content.code, 1435002 ]
      - eq: [ "${contain_keys($message,普通手绘)}", False ]

- test:
    name: "fdaLanguageMode为英文时传普通手绘"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaLanguageMode": "ENGLISH" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 0 ]

- test:
    name: "同时存在签署声明文件"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaLanguageMode": "ENGLISH" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 ,"signTipsFileId": "${file_2}", "signTipsTitle": "签署声明" },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 0 ]

- test:
    name: "fdaSigningReason存在空值不允许发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaLanguageMode": "ENGLISH","fdaSigningReason": [ "","GET OUT OF MY OFFICE AND GO GWAY!!!" ], },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, fdaSigningReason不允许传入空字符串 ]

- test:
    name: "fdaSigningReason存在emoji不允许发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaLanguageMode": "ENGLISH","fdaSigningReason": [ "😀" ], },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, fdaSigningReason包含不支持的字符，请修改后重试！ ]

- test:
    name: "fdaSigningReason重复不允许发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaLanguageMode": "ENGLISH","fdaSigningReason": [ "GET OUT OF MY OFFICE AND GO GWAY!!!","GET OUT OF MY OFFICE AND GO GWAY!!!" ], },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, fdaSigningReason不允许重复 ]

- test:
    name: "英文版不支持36个字符的fdaSigningReason"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaLanguageMode": "ENGLISH","fdaSigningReason": [ "GET OUT OF MY OFFICE AND GO GWAY!!!!" ], },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, fdaSigningReason单条长度不能超过35个字符 ]

- test:
    name: "中文版不支持16个字符的fdaSigningReason"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaSigningReason": [ "一去二三里烟村四五家亭台六七座八" ], },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, fdaSigningReason单条长度不能超过15个字符 ]

- test:
    name: "中文版支持15个字符的fdaSigningReason+普通手绘+AI手绘"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaSigningReason": [ "一去二三里烟村四五家亭台六七座" ,"八九十枝花危楼高百尺手可摘星辰","不敢高声语恐惊天上人离离原上草","一岁一枯荣野火烧不尽春风吹又生","远芳侵古道晴翠接芳城又送王孙去" ], },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,2","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 0 ]

- test:
    name: "不支持模板章"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1,2","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, 只允许使用手绘章 ]

- test:
    name: "英文版支持35字符的fdaSigningReason"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaLanguageMode": "ENGLISH","fdaSigningReason": [ "GET OUT OF MY OFFICE AND GO GWAY!!!","GET OUT OF MY OFFICE AND GO GWAY...","GET OUT OF MY OFFICE AND GO GWAY,,,","GET OUT OF MY OFFICE AND GO GWAY~~~","GET OUT OF MY OFFICE AND GO GWAY###" ], },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 0 ]

- test:
    name: "不能超过5条fdaSigningReason"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType","fdaLanguageMode": "ENGLISH","fdaSigningReason": [ "GET OUT OF MY OFFICE AND GO GWAY!!!","GET OUT OF MY OFFICE AND GO GWAY...","GET OUT OF MY OFFICE AND GO GWAY,,,","GET OUT OF MY OFFICE AND GO GWAY~~~","GET OUT OF MY OFFICE AND GO GWAY###","GET OUT OF MY OFFICE AND GO GWAY￥￥￥" ], },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, fdaSigningReason不允许超过5条 ]

- test:
    name: "不支持仅企业签"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "orgSignerInfo": { "orgId": "${account_id_sx_tsign}","transactorInfo": { "psnId": "${account_id_2_tsign}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1,2","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 1 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, FDA签名要求每个签署方至少包含一个经办人章或个人章 ]

- test:
    name: "不支持仅法人签"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "orgSignerInfo": { "orgId": "${account_id_sx_tsign}","transactorInfo": { "psnId": "${account_id_2_tsign}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1,2","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 2 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, FDA签名要求每个签署方至少包含一个经办人章或个人章 ]

- test:
    name: "企业+经办人可以发起FDA签署"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "orgSignerInfo": { "orgId": "${account_id_sx_tsign}","transactorInfo": { "psnId": "${account_id_2_tsign}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1,2","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 1 }, { "orgSignerInfo": { "orgId": "${account_id_sx_tsign}","transactorInfo": { "psnId": "${account_id_2_tsign}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 3 } ] }
    validate:
      - eq: [ content.code, 0 ]

- test:
    name: "企业+经办人骑缝不可以发起FDA签署"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "orgSignerInfo": { "orgId": "${account_id_sx_tsign}","transactorInfo": { "psnId": "${account_id_2_tsign}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1,2","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 1 }, { "orgSignerInfo": { "orgId": "${account_id_sx_tsign}","transactorInfo": { "psnId": "${account_id_2_tsign}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 2 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 3 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, 不允许使用骑缝章 ]

- test:
    name: "多文件只指定了一份FDA签署"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileId": "$file_id" }, { "fileId": "$file_2" } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, FDA签名要求每个签署方至少包含一个经办人章或个人章 ]

- test:
    name: "多文件不同顺序指定FDA签署"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileId": "$file_id" }, { "fileId": "$file_2" } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 },{ "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 2 },"signFields": [ { "customBizNum": "","fileId": "$file_2","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    extract:
      - message: content.message
    validate:
      - eq: [ content.code, 0 ]

- test:
    name: "多文件相同顺序指定FDA签署"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileId": "$file_id" }, { "fileId": "$file_2" } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 },{ "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_2","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 0 ]

- test:
    name: "海外签globalWillingness为false不能发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileId": "$file_id" } ],"signFlowConfig": { "authConfig": { "globalWillingness": false },"autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" ,"signMode": "GLOBAL" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "authConfig": { "globalWillingness": false }, "psnSignerInfo": { "psnId": "${account_id_2_tsign}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "", "signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, "signMode为GLOBAL并且signType为FDAType,globalWillingness必须为true" ]

- test:
    name: 获取发起流程页面链接-不支持FDA
    variables:
      - json: { "signFlowConfig": { "signFlowTitle": "签署流程" , "signConfig": { "signType": "FDAType" } } }
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - ne: [ "content.code",0 ]

- test:
    name: "同一个签署方传了两个signer对象，分布为备注签和普通签署区，可以发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true,"signType": "FDAType" },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signers": [ { "psnSignerInfo": { "psnAccount": "${phone_1}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,2","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 }, { "psnSignerInfo": { "psnAccount": "${phone_1}" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id", "remarkSignFieldConfig": { "aiCheck": 0,"freeMode": false,"inputType": 2,"movableSignField": true,"remarkContent": "备注签","remarkFontSize1": 20,"signFieldHeight": 100,"signFieldPosition": { "acrossPageMode": "","acrossPageOffset": 0,"positionPage": "1","positionX": 200,"positionY": 600 },"signFieldWidth": 100 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 1 } ],"signerType": 0 } ] }
    validate:
      - eq: [ content.code, 0 ]