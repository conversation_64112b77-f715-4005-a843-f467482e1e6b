- config:
    name: "静默授权"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - group: ${ENV(envCode)}
      - env: ${ENV(env)}
      - file: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_password: ${ENV(file_with_password_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - psn_1: ${ENV(tengqing_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - org_1: ${ENV(org_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - org_2: ${ENV(org_id_2_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - org_name: ${ENV(xuanyuan_sixian_name)}
      - org_code: ${ENV(sixian_org_code)}
      - org_1_legal_psnId: ${ENV(legal_psnId_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_legal: "54f7a943c05242d7949cc0718ec36814"

- test:
    name: 分布发起流程
    variables:
      - json: { "autoArchive": "false","businessScene": "签署${get_timestamp()}","configInfo": { "noticeType": "1","noticeDeveloperUrl": "","signPlatform": "1,2,3","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false },"extend": { } }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]

- test:
    name: 追加文件到流程
    variables:
      - flowId: $sign_flowId
      - docs: [ { fileId: "${file_password}", fileName: "文档.pdf", filePassword: "",encryption: 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]

- test:
    name: "添加平台自动签签署区"
    variables:
      - posBean: { 'posPage': '1','posX': 100, 'posY': 100 }
      - signfield: { 'fileId': "${file_password}", 'order': 1,  'posBean': $posBean, 'signType': 1 }
      - signfields: [ $signfield ]
      - flowId: $sign_flowId
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 签署失败返回99
    api: api/signflow/flowSigners/get_flow_signers.yml
    variables:
      - flowId: $sign_flowId
      - X-Tsign-Client-Id: 1
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.signers.0.signStatus",99 ]

- test:
    name: authMethod非枚举值
    variables:
      - json: { "appId": "${app_id}","authMethod": "100","fileType": "fileType","notifyUrl": "","redirectUrl": "https://www.baidu.com?a=1&b=2","sealScope": "sealScope","validDays": 1 }
    api: api/RPC/createSignAuthUrl/input.yml
    validate:
      - eq: [ status_code, 503 ]
      - contains: [ "content.message",authMethod参数不合法 ]

- test:
    name: 获取链接
    variables:
      - json: { "appId": "${app_id}","authMethod": "1","fileType": "fileType","notifyUrl": "","redirectUrl": "https://www.baidu.com?a=1&b=2","sealScope": "sealScope","validDays": 1 }
      - index1: 'contextId='
      - index2: '"'
    api: api/RPC/createSignAuthUrl/input.yml
    validate:
      - eq: [ status_code, 200 ]
      - contains: [ "content.data.signAuthUrl",silence ]
    extract:
      - signAuthUrl: content.data.signAuthUrl

- test:
    name: 获取contextId
    variables:
      - json: { "key": "contextId","value": "${signAuthUrl}","start": "contextId=","end": "" }
    api: api/utils/demosdk/substring.yml
    extract:
      - contextId: content.contextId

- test:
    name: 授权页面初始化信息接口
    variables:
      - params: contextId=${contextId}
    api: api/v1/signAuth/initPageInfo.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.authMethod",1 ]

- test:
    name: 获取核身链接
    variables:
      - json: { "orgName": "${org_name}","orgCardType": "CRED_ORG_USCC","orgCardNo": "${org_code}","transactorAccount": "1","contextId": "$contextId","sealScope": "111" }
    api: api/v1/signAuth/createIdentityAuthUrl.yml
    validate:
      - eq: [ status_code, 200 ]
      - contains: [ "content.data.identityUrl",identity ]

- test:
    name: 发起线上授权，获取authId
    variables:
      - json: { "accountId": "${org_1}","fileType": "1","notifyUrl": "","redirectUrl": "","sealScope": "111","sendNotice": true,"transactorAccountId": "${psn_1}","validDate": "********" }
    api: api/v1/signAuthApi/online.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - authId: content.data.authId

- test:
    name: v2一步发起流程，未授权无法发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",用户未授权或授权已过期 ]

- test:
    name: 静默授权-老
    variables:
      - accountId: ${org_1}
      - deadline: "2099-12-31 23:59:59"
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: v2一步发起流程，可以发起
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - flowId: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: "查询流程详情-已归档"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.signFlowStatus",2 ]

- test:
    name: v2一步发起流程，相同gid不同oid未授权无法发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_2}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",用户未授权或授权已过期 ]
      - contains: [ "content.message","${org_2}" ]

- test:
    name: 取消授权-老
    variables:
      - accountId: ${org_1}
      - type: silent
    api: api/signflow/signAuth/signAuth_delete.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: v2一步发起流程，取消授权无法发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",用户未授权或授权已过期 ]
      - contains: [ "content.message","${org_1}" ]

- test:
    name: 查询授权记录-进行中
    variables:
      - params: authId=${authId}
    api: api/v1/signAuthApi/queryAuthResult.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.status",1 ]

- test:
    name: 获取授权书签署链接
    variables:
      - json: { "authId": "${authId}","needLogin": false }
    api: api/v1/signAuthApi/signUrl.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - longUrl: content.data.longUrl

- test:
    name: 获取flowId
    variables:
      - json: { "key": "flowId","value": "${longUrl}","start": "flowId=","end": "&organ=" }
    api: api/utils/demosdk/substring.yml
    extract:
      - flowId: content.flowId

- test:
    name: 签署授权书
    variables:
      - json: { "env": "${env}", "flowId": "${flowId}","group": "${group}" }
    api: api/utils/demosdk/sign/start.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.success",True ]

#- test:
#    name: 签署授权书
#    variables:
#      - params: env=${env}&flow_id=${flowId}&group=${group}
#    api: api/utils/mcp/signing/one_click_sign.yml
#    teardown_hooks:
#      - ${hook_sleep_n_secs(3)}

- test:
    name: 查询授权记录-已授权
    variables:
      - params: authId=${authId}
    api: api/v1/signAuthApi/queryAuthResult.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.status",2 ]

- test:
    name: v2一步发起流程，可以发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: "查询流程详情-已归档"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.signFlowStatus",2 ]

- test:
    name: v2一步发起流程，相同gid不同oid可以发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_2}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: "查询流程详情-已归档"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.signFlowStatus",2 ]

- test:
    name: 取消静默授权
    variables:
      - json: { "authId": "${authId}" }
    api: api/v1/signAuthApi/cancelAuth.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查询授权记录-已取消
    variables:
      - params: authId=${authId}
    api: api/v1/signAuthApi/queryAuthResult.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.status",4 ]

- test:
    name: v2一步发起流程，取消授权无法发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",用户未授权或授权已过期 ]

- test:
    name: 发起企业授权书线下签署任务
    variables:
      - json: { "accountId": "${org_1}","fileType": "1","notifyUrl": "","redirectUrl": "","sealScope": "111","sendNotice": true, "validDate": "********" }
    api: api/v1/signAuthApi/offline.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - authId: content.data.authId

- test:
    name: v2一步发起流程，未授权无法发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",用户未授权或授权已过期 ]

- test:
    name: 查询授权记录
    variables:
      - params: authId=${authId}
    api: api/v1/signAuthApi/queryAuthResult.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.status",1 ]

- test:
    name: 上传已盖章授权书文件
    variables:
      - json: { "authId": "${authId}", "fileId": "${file}" }
    api: api/v1/signAuthApi/upload.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}

- test:
    name: 查询授权记录
    variables:
      - params: authId=${authId}
    api: api/v1/signAuthApi/queryAuthResult.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.status",2 ]

- test:
    name: v2一步发起流程，可以发起
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: "查询流程详情-已归档"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.signFlowStatus",2 ]

- test:
    name: v2一步发起流程，相同gid不同oid可以发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_2}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: "查询流程详情-已归档"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.signFlowStatus",2 ]

- test:
    name: 取消静默授权
    variables:
      - json: { "authId": "${authId}" }
    api: api/v1/signAuthApi/cancelAuth.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查询授权记录
    variables:
      - params: authId=${authId}
    api: api/v1/signAuthApi/queryAuthResult.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.status",4 ]

- test:
    name: v2一步发起流程，取消授权后无法发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",用户未授权或授权已过期 ]

- test:
    name: 发起法人授权书线下签署任务
    variables:
      - json: { "authType": "LEGALREP", "legalRepAccountId": "${org_1_legal_psnId}", "accountId": "${org_1}","fileType": "1","notifyUrl": "","redirectUrl": "","sealScope": "111","sendNotice": true, "validDate": "********" }
    api: api/v1/signAuthApi/offline.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - legal_authId: content.data.authId

- test:
    name: v2一步发起流程，未授权无法发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "V2个人自动签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1_legal_psnId}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 0,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",用户未授权或授权已过期 ]

- test:
    name: 查询授权记录
    variables:
      - params: authId=${legal_authId}
    api: api/v1/signAuthApi/queryAuthResult.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.status",1 ]

- test:
    name: 上传已盖章授权书文件-法人授权
    variables:
      - json: { "authId": "${legal_authId}", "fileId": "${file_legal}" }
    api: api/v1/signAuthApi/upload.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}

- test:
    name: 查询授权记录
    variables:
      - params: authId=${legal_authId}
    api: api/v1/signAuthApi/queryAuthResult.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.status",2 ]

- test:
    name: v2一步发起流程，已授权可以发起个人自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "V2个人自动签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1_legal_psnId}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 0,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]
    extract:
      - flowId: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

# 历史问题，暂未解决
#- test:
#    name: v2一步发起流程，有法人静默授权-无企业授权，不可发起企业静默签署
#    variables:
#      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "V2个人自动签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1}","signerAccountId": "${org_1_legal_psnId}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
#    api: api/iterate_cases/createFlowOneStep.yml
#    validate:
#      - eq: [ status_code, 200 ]
#      - eq: [ "content.code",1435002 ]
#      - contains: [ "content.message",用户未授权或授权已过期]
#    extract:
#      - flowId: content.data.flowId
#    teardown_hooks:
#      - ${hook_sleep_n_secs(3)}

- test:
    name: "查询流程详情-已归档"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.signFlowStatus",2 ]

- test:
    name: 取消静默授权
    variables:
      - json: { "authId": "${legal_authId}" }
    api: api/v1/signAuthApi/cancelAuth.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查询授权记录
    variables:
      - params: authId=${legal_authId}
    api: api/v1/signAuthApi/queryAuthResult.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.status",4 ]

- test:
    name: v2一步发起流程，未授权无法发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "V2个人自动签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1_legal_psnId}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 0,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",用户未授权或授权已过期 ]