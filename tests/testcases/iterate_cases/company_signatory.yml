- config:
    name: 通过文件创建签署流程-非实名流程
    base_url: ${ENV(base_url)}
    variables:
      - app_id: "3438757422"
      - app_Id1: "3438757422"



- test:
    name: "通过文件创建流程 （个人企业均未实名-异常报错"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {
    "docs": [
        {
            "fileEditPwd": "",
            "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
            "fileName": "个人借贷合同.pdf",
            "neededPwd": false
        },
        {
            "fileId": "232d475467a84ce7b5c42926440a2779"
        }
    ],
    "copiers": [],
    "signFlowInitiator": {
        "orgInitiator": {
            "orgId": "705789a4bc6545e7b9cdc4ebadafc177",
            "transactor": {
                "psnId": "ab9e503fb5b24b988da20e2a718a552a"
            }
        }
    },
    "signFlowConfig": {
        "authConfig": {
            "audioVideoTemplateId": "",
            "orgAvailableAuthModes": [],
            "orgEditableFields": [],
            "psnAvailableAuthModes": [],
            "psnEditableFields": [],
            "willingnessAuthModes": []
        },
        "autoFinish": true,
        "autoStart": true,
        "chargeConfig": {
            "chargeMode": 1
        },
        "noticeConfig": {
            "noticeTypes": "1"
        },
        "notifyUrl": "{{notifyUrl}}",
        "redirectConfig": {
            "redirectDelayTime": null,
            "redirectUrl": ""
        },
        "signConfig": {
            "availableSignClientTypes": "",
            "showBatchDropSealButton": true
        },
        "signFlowTitle": "经办人自由签 + 企业自由签 + 个人自由签",
        "signFlowExpireTime": ""
    },
    "signers": [
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": {
                "orgId": "",
                "orgName": "esigntest浙江浙农实业投资有限公司",
                "orgInfo": null,
                "transactorInfo": {
                    "psnAccount": "***********",
                    "psnId": "",
                    "psnInfo": {
                        "psnName": ""
                    }
                }
            },
            "psnSignerInfo": null,
            "signConfig": {
                "signOrder": 1
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": true,"adaptableSignFieldSize":true,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
                        "signFieldPosition": {
                            "acrossPageMode": "",
                            "positionPage": "1",
                            "positionX": 100,
                            "positionY": 100
                        },
                        "signFieldSize": null,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
                        "fontSize": 0,
                        "showSignDate": 1,
                        "signDatePositionPage": 2,
                        "signDatePositionX": 100,
                        "signDatePositionY": 100
                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 3
        },
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": {
                "orgId": "",
                "orgName": "esigntest浙江浙农实业投资有限公司",
                "orgInfo": null,
                "transactorInfo": {
                    "psnAccount": "***********",
                    "psnId": "",
                    "psnInfo": {
                        "psnName": ""
                    }
                }
            },
            "psnSignerInfo": null,
            "signConfig": {
                "signOrder": 1
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": true,"adaptableSignFieldSize":true,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
                        "signFieldPosition": {
                            "acrossPageMode": "",
                            "positionPage": "1",
                            "positionX": 100,
                            "positionY": 100
                        },
                        "signFieldSize": null,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
                        "fontSize": 0,
                        "showSignDate": 1,
                        "signDatePositionPage": 2,
                        "signDatePositionX": 100,
                        "signDatePositionY": 100
                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 1
        },
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": null,
            "psnSignerInfo": {
                "psnId": "",
                "psnAccount": "***********"
            },
            "signConfig": {
                "signOrder": 1
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": true,"adaptableSignFieldSize":true,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
                        "signFieldPosition": {
                            "acrossPageMode": "",
                            "positionPage": "1",
                            "positionX": 100,
                            "positionY": 100
                        },
#                        "signFieldSize": 200,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
                        "fontSize": 0,
                        "showSignDate": 1,
                        "signDatePositionPage": 2,
                        "signDatePositionX": 100,
                        "signDatePositionY": 100
                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 0
        }
    ]
}

    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, "自由模式"]




- test:
    name: "通过文件创建流程 （个人企业均未实名-正常"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {
    "docs": [
        {
            "fileEditPwd": "",
            "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
            "fileName": "个人借贷合同.pdf",
            "neededPwd": false
        },
        {
            "fileId": "232d475467a84ce7b5c42926440a2779"
        }
    ],
    "copiers": [],
    "signFlowInitiator": {
        "orgInitiator": {
            "orgId": "705789a4bc6545e7b9cdc4ebadafc177",
            "transactor": {
                "psnId": "ab9e503fb5b24b988da20e2a718a552a"
            }
        }
    },
    "signFlowConfig": {
        "authConfig": {
            "audioVideoTemplateId": "",
            "orgAvailableAuthModes": [],
            "orgEditableFields": [],
            "psnAvailableAuthModes": [],
            "psnEditableFields": [],
            "willingnessAuthModes": []
        },
        "autoFinish": true,
        "autoStart": true,
        "chargeConfig": {
            "chargeMode": 1
        },
        "noticeConfig": {
            "noticeTypes": "1"
        },
        "notifyUrl": "{{notifyUrl}}",
        "redirectConfig": {
            "redirectDelayTime": null,
            "redirectUrl": ""
        },
        "signConfig": {
            "availableSignClientTypes": "",
            "showBatchDropSealButton": true
        },
        "signFlowTitle": "经办人自由签 + 企业自由签 + 个人自由签",
        "signFlowExpireTime": ""
    },
    "signers": [
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": {
                "orgId": "",
                "orgName": "esigntest浙江浙农实业投资有限公司",
                "orgInfo": null,
                "transactorInfo": {
                    "psnAccount": "***********",
                    "psnId": "",
                    "psnInfo": {
                        "psnName": ""
                    }
                }
            },
            "psnSignerInfo": null,
            "signConfig": {
                "signOrder": 1
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": true,"adaptableSignFieldSize":true,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
#                        "signFieldPosition": {
#                            "acrossPageMode": "",
#                            "positionPage": "1",
#                            "positionX": 100,
#                            "positionY": 100
#                        },
#                        "signFieldSize": null,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
#                        "fontSize": 0,
                        "showSignDate": 1,
#                        "signDatePositionPage": 2,
#                        "signDatePositionX": 100,
#                        "signDatePositionY": 100
                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 3
        },
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": {
                "orgId": "",
                "orgName": "esigntest浙江浙农实业投资有限公司",
                "orgInfo": null,
                "transactorInfo": {
                    "psnAccount": "***********",
                    "psnId": "",
                    "psnInfo": {
                        "psnName": ""
                    }
                }
            },
            "psnSignerInfo": null,
            "signConfig": {
                "signOrder": 1
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": true,"adaptableSignFieldSize":true,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
#                        "signFieldPosition": {
#                            "acrossPageMode": "",
#                            "positionPage": "1",
#                            "positionX": 100,
#                            "positionY": 100
#                        },
#                        "signFieldSize": null,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
                        "showSignDate": 1,

                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 1
        },
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": null,
            "psnSignerInfo": {
                "psnId": "",
                "psnAccount": "***********"
            },
            "signConfig": {
                "signOrder": 1
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": true,"adaptableSignFieldSize":true,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
#                        "signFieldPosition": {
#                            "acrossPageMode": "",
#                            "positionPage": "1",
#                            "positionX": 100,
#                            "positionY": 100
#                        },
#                        "signFieldSize": 200,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
                        "showSignDate": 1,

                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 0
        }
    ]
}
    extract:
      - signFlowId: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
