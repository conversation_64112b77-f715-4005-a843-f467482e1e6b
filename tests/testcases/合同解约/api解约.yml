- config:
    name: "api3.0合同解约"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - group: ${ENV(envCode)}
      - fileId1: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - fileId2: ${ENV(file_id_2_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - psn_id_1: ${ENV(account_id1_in_tsign)}
      - psn_id_2: ${ENV(account_id2_in_tsign)}
      - org_id_1: ${ENV(orgid_dy)}
      - org_id_2: ${ENV(orgid_sx)}
      - env: ${ENV(env)}

- test:
    name: 发起流程
    variables:
      - docs: [ { "fileId": "${fileId1}" },{ "fileId": "${fileId2}" } ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}",initiatorAccountId: "${psn_id_1}", initiatorAuthorizedAccountId: "${psn_id_1}" }
      - signers: [ { signOrder: 1, signerAccount: { signerAccountId: "${psn_id_1}", authorizedAccountId: "${psn_id_1}" }, signfields: [ { actorIndentityType: 0,fileId: "${fileId1}", posBean: { posPage: "1", posX: 200, posY: 200 },sealType: "", signDateBeanType: "1",signDateBean: { fontSize: 20, posX: 220, posY: 250 }, signType: 1, width: 150 } ], thirdOrderNo: "111" },{ signOrder: 1, signerAccount: { signerAccountId: "${psn_id_1}", authorizedAccountId: "${org_id_1}" }, signfields: [ { actorIndentityType: 2,fileId: $fileId2, posBean: { posPage: "1", posX: 200, posY: 200 },sealType: "", signDateBeanType: "1",signDateBean: { fontSize: 20, posX: 220, posY: 250 }, signType: 1, width: 150 } ], thirdOrderNo: "111" },{ signOrder: 1, signerAccount: { signerAccountId: "${psn_id_2}", authorizedAccountId: "${org_id_2}" }, signfields: [ { actorIndentityType: 2,fileId: $fileId2, posBean: { posPage: "1", posX: 200, posY: 200 },sealType: "", signDateBeanType: "1",signDateBean: { fontSize: 20, posX: 220, posY: 250 }, signType: 1, width: 150 } ], thirdOrderNo: "111" } ]
      - json: { docs: $docs,flowInfo: $flowInfo, signers: $signers }
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]

- test:
    name: "流程未完成-无解约按钮"
    variables:
      - flowId: ${sign_flowId}
      - queryAccountId: ${psn_id_1}
      - resourceShareId: ""
    api: api/share/config.yml
    extract:
      - response: content.data.pageConfig.configList
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "${contain_keys($response,解约)}", False ]

- test:
    name: 获取解约链接-流程未完结
    variables:
      - flowId: ${sign_flowId}
      - json: { "rescissionInitiator": { "orgInitiator": { "orgId": "","transactor": { "psnId": "" } },"psnInitiator": { "psnId": "${psn_id_1}" } } }
    api: api/v3/sign-flow/signFlowId/rescission-url.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1439100 ]
      - contains: [ "content.message",非完结状态 ]


- test:
    name: 签署原流程
    variables:
      - json: { "env": "${env}", "flowId": "${sign_flowId}","group": "${group}" }
    api: api/utils/demosdk/sign/start.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.success",True ]

#- test:
#    name: 签署原流程
#    variables:
#      - params: env=${env}&flow_id=${sign_flowId}&group=${group}
#    api: api/utils/mcp/signing/one_click_sign.yml
#    teardown_hooks:
#      - ${hook_sleep_n_secs(3)}

- test:
    name: 原流程发起人-可以获取解约链接
    variables:
      - flowId: ${sign_flowId}
      - json: { "rescissionInitiator": { "orgInitiator": { "orgId": "","transactor": { "psnId": "" } },"psnInitiator": { "psnId": "${psn_id_1}" } } }
    api: api/v3/sign-flow/signFlowId/rescission-url.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: 获取解约链接-非标准签用户不能获取链接
    variables:
      - flowId: ${sign_flowId}
      - json: { "rescissionInitiator": { "orgInitiator": { "orgId": "","transactor": { "psnId": "" } },"psnInitiator": { "psnId": "${ENV(caogu)}" } } }
    api: api/v3/sign-flow/signFlowId/rescission-url.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",解约发起人账号未注册 ]

- test:
    name: 获取解约链接-非签约主体
    variables:
      - flowId: ${sign_flowId}
      - json: { "rescissionInitiator": { "orgInitiator": { "orgId": "","transactor": { "psnId": "" } },"psnInitiator": { "psnId": "${psn_id_2}" } } }
    api: api/v3/sign-flow/signFlowId/rescission-url.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1439106 ]
      - contains: [ "content.message",非原流程签署主体 ]

- test:
    name: api发起解约
    variables:
      - flowId: ${sign_flowId}
      - json: { "rescindFileList": [ "${fileId2}" ],"rescindReason": 2,"rescindReasonNotes": "333","rescissionInitiator": { "psnInitiator": { "psnId": "${psn_id_1}" } },"signFlowConfig": { "noticeConfig": { "examineNotice": true,"noticeTypes": "1" },"signConfig": { "availableSignClientTypes": "2" } } }
    api: api/v3/sign-flow/signFlowId/initiate-rescission.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]
    extract:
      - rescission_sign_flowId: content.data.signFlowId

- test:
    name: 将解约流程过期
    variables:
      - json: { "flowId": "${rescission_sign_flowId}" }
    api: api/RPC/expireFlow.yml
    validate:
      - eq: [ status_code, 200 ]

- test:
    name: "流程已完成-有解约按钮"
    variables:
      - flowId: ${sign_flowId}
      - queryAccountId: ${psn_id_1}
      - resourceShareId: ""
    api: api/share/config.yml
    extract:
      - response: content.data.pageConfig.configList
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "${contain_keys($response,解约)}", True ]

- test:
    name: 获取解约链接-成功
    variables:
      - flowId: ${sign_flowId}
      - json: { "rescissionInitiator": { "orgInitiator": { "orgId": "${org_id_1}","transactor": { "psnId": "${psn_id_1}" } },"psnInitiator": { "psnId": "" } } }
    api: api/v3/sign-flow/signFlowId/rescission-url.yml
    extract:
      - rescission_url: content.data.rescissionUrl
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]
      - eq: [ "content.data.rescissionUrl", $rescission_url ]

- test:
    name: 发起合同解约-未全部参与方，无法发起
    variables:
      - json: { "additionalInfo": "附加信息","businessType": 1,"docs": [ { "fileId": "${fileId2}" } ],"originFlowId": "${sign_flowId}","reason": "解约信息","signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true },"signFlowTitle": "解约流程" },"signFlowInitiator": { "orgInitiator": { "orgId": "${org_id_1}","transactor": { "psnId": "${psn_id_1}" } } },"signers": [ { "orgSignerInfo": { "orgId": "${org_id_1}","transactorInfo": { "psnId": "${psn_id_1}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signerType": 1 } ] }
    api: api/v3/rescission/create.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",原流程文件签署方与解约参与方不一致 ]

- test:
    name: 发起合同解约
    variables:
      - json: { "additionalInfo": "附加信息","businessType": 1,"docs": [ { "fileId": "${fileId2}" } ],"originFlowId": "${sign_flowId}","reason": "解约信息","signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true },"signFlowTitle": "解约流程" },"signFlowInitiator": { "orgInitiator": { "orgId": "${org_id_1}","transactor": { "psnId": "${psn_id_1}" } } },"signers": [ { "orgSignerInfo": { "orgId": "${org_id_1}","transactorInfo": { "psnId": "${psn_id_1}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signerType": 1 },{ "orgSignerInfo": { "orgId": "${org_id_2}","transactorInfo": { "psnId": "${psn_id_2}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signerType": 1 } ] }
    api: api/v3/rescission/create.yml
    extract:
      - rescission_flowId: content.data.signFlowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]


- test:
    name: 签署解约流程
    variables:
      - json: { "env": "${env}", "flowId": "${rescission_flowId}","group": "${group}" }
    api: api/utils/demosdk/sign/start.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.success",True ]

#- test:
#    name: 签署解约流程
#    variables:
#      - params: env=${env}&flow_id=${rescission_flowId}&group=${group}
#    api: api/utils/mcp/signing/one_click_sign.yml
#    teardown_hooks:
#      - ${hook_sleep_n_secs(3)}

- test:
    name: 3.0 发起流程
    variables:
      - json: { "docs": [ { "fileId": "${fileId2}" } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"signFlowTitle": "签署流程" },"signers": [ { "noticeConfig": { "noticeTypes": "1,2" },"orgSignerInfo": { "orgId": "${org_id_1}","transactorInfo": { "psnId": "${psn_id_1}" } },"signFields": [ { "fileId": "${fileId2}","normalSignFieldConfig": { "signFieldPosition": { "positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signFieldType": 0 } ],"signerType": 1 },{ "noticeConfig": { "noticeTypes": "1,2" },"orgSignerInfo": { "orgId": "${org_id_2}","transactorInfo": { "psnId": "${psn_id_2}" } },"signFields": [ { "fileId": "${fileId2}","normalSignFieldConfig": { "signFieldPosition": { "positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signFieldType": 0 } ],"signerType": 1 } ] }
    api: api/sign-flow/create-by-file.yml
    extract:
      - sign_flowId: content.data.signFlowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取解约链接-流程未完结
    variables:
      - flowId: ${sign_flowId}
      - json: { "rescissionInitiator": { "orgInitiator": { "orgId": "","transactor": { "psnId": "" } },"psnInitiator": { "psnId": "${psn_id_1}" } } }
    api: api/v3/sign-flow/signFlowId/rescission-url.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1439100 ]
      - contains: [ "content.message",非完结状态 ]


- test:
    name: 签署原流程
    variables:
      - json: { "env": "${env}", "flowId": "${sign_flowId}","group": "${group}" }
    api: api/utils/demosdk/sign/start.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.success",True ]

#- test:
#    name: 签署原流程
#    variables:
#      - params: env=${env}&flow_id=${sign_flowId}&group=${group}
#    api: api/utils/mcp/signing/one_click_sign.yml
#    teardown_hooks:
#      - ${hook_sleep_n_secs(3)}

- test:
    name: 获取解约链接-成功
    variables:
      - flowId: ${sign_flowId}
      - json: { "rescissionInitiator": { "orgInitiator": { "orgId": "${org_id_1}","transactor": { "psnId": "${psn_id_1}" } },"psnInitiator": { "psnId": "" } } }
    api: api/v3/sign-flow/signFlowId/rescission-url.yml
    extract:
      - rescission_url: content.data.rescissionUrl
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]
      - eq: [ "content.data.rescissionUrl", $rescission_url ]

- test:
    name: 发起合同解约
    variables:
      - json: { "additionalInfo": "附加信息","businessType": 1,"docs": [ { "fileId": "${fileId2}" } ],"originFlowId": "${sign_flowId}","reason": "解约信息","signFlowConfig": { "autoFinish": true,"autoStart": true,"signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true },"signFlowTitle": "解约流程" },"signFlowInitiator": { "orgInitiator": { "orgId": "${org_id_1}","transactor": { "psnId": "${psn_id_1}" } } },"signers": [ { "orgSignerInfo": { "orgId": "${org_id_1}","transactorInfo": { "psnId": "${psn_id_1}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signerType": 1 },{ "orgSignerInfo": { "orgId": "${org_id_2}","transactorInfo": { "psnId": "${psn_id_2}" } },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signerType": 1 } ] }
    api: api/v3/rescission/create.yml
    extract:
      - rescission_flowId: content.data.signFlowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 获取流程详情-解约中
    variables:
      - flowId: ${sign_flowId}
    api: api/sign-flow/detail.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.rescissionStatus",1 ]
      - eq: [ "content.data.rescissionSignFlowIds.0","${rescission_flowId}" ]
      - len_gt: [ "content.data.signers.0.orgSigner.transactor.psnName", 1 ]

- test:
    name: "v2查询原流程-解约中"
    variables:
      - flowId: ${sign_flowId}
      - queryAccountId: ""
      - resourceShareId: ""
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "content.data.extFlowStatus",13 ]

- test:
    name: 预览解约协议
    variables:
      - signFlowId: ${sign_flowId}
      - json: { "additionalInformation": "解约信息解约信息解约信息","initiatorSubjectId": "${org_id_1}","rescissionFileIds": [ "${fileId2}" ],"rescissionReason": "解约原因解约原因" }
    api: api/v3/rescission/signFlowId/file/preview.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取解约发起页信息
    variables:
      - flowId: ${rescission_flowId}
      - json: { "initiatorAccount": { "initiatorAccountId": "${psn_id_1}","initiatorIdentityAccountType": 0,"initiatorSubjectAccountId": "${psn_id_1}" } }
    api: api/v3/rescission/signFlowId/getInitPageInfo.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]


- test:
    name: 签署解约流程
    variables:
      - json: { "env": "${env}", "flowId": "${rescission_flowId}","group": "${group}" }
    api: api/utils/demosdk/sign/start.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.success",True ]

#- test:
#    name: 签署解约流程
#    variables:
#      - params: env=${env}&flow_id=${rescission_flowId}&group=${group}
#    api: api/utils/mcp/signing/one_click_sign.yml
#    teardown_hooks:
#      - ${hook_sleep_n_secs(3)}

- test:
    name: 获取流程详情-已解约
    variables:
      - flowId: ${sign_flowId}
    api: api/sign-flow/detail.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.rescissionStatus",3 ]
      - eq: [ "content.data.rescissionSignFlowIds.0","${rescission_flowId}" ]

- test:
    name: "v2查询原流程-已解约"
    variables:
      - flowId: ${sign_flowId}
      - queryAccountId: ""
      - resourceShareId: ""
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "content.data.extFlowStatus",12 ]


- test:
    name: "流程已解约-无解约按钮"
    variables:
      - flowId: ${sign_flowId}
      - queryAccountId: ${psn_id_1}
      - resourceShareId: ""
    api: api/share/config.yml
    extract:
      - response: content.data.pageConfig.configList
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "${contain_keys($response,解约)}", False ]

- test:
    name: 查询流程文档 - 签署完成后的流程文档的filekey
    variables:
      - flowId: ${sign_flowId}
      - step: 0
    api: api/iterate_cases/queryFlowInfoWithDocEvi.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - signed_file_key: content.data.attachmentList.0.fileKey

- test:
    name: 根据filekey获取签署完成的fileId
    variables:
      - accountId: ""
      - fileName: "7页.pdf"
      - fileKey: $signed_file_key
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - signed_file_id: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]


- test:
    setup_hooks:
      - ${sleep_N_secs(3)}
    name: 签署完成流程文档验签 - 使用官网验签接口
    api: api/signflow/signflows/verifySignature.yml
    variables:
      - fileId: $signed_file_id
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "content.data.fileInfo.fileStatus",4 ]
      - eq: [ "content.data.fileInfo.fileStatusDesc",已解约 ]
      - str_eq: [ "content.data.signatures.0.signature.validate",true ]
      - str_eq: [ "content.data.signatures.0.signature.modify",false ]

- test:
    name: 发起流程
    variables:
      - docs: [ { "fileId": "${fileId1}" },{ "fileId": "${fileId2}" } ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}",initiatorAccountId: "${psn_id_1}", initiatorAuthorizedAccountId: "${psn_id_1}" }
      - signers: [ { signOrder: 1, signerAccount: { signerAccountId: "${psn_id_1}", authorizedAccountId: "${psn_id_1}" }, signfields: [ { actorIndentityType: 0,fileId: "${fileId1}", posBean: { posPage: "1", posX: 200, posY: 200 },sealType: "", signDateBeanType: "1",signDateBean: { fontSize: 20, posX: 220, posY: 250 }, signType: 1, width: 150 } ], thirdOrderNo: "111" } ]
      - json: { docs: $docs,flowInfo: $flowInfo, signers: $signers }
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]

- test:
    name: 获取签名域信息-原流程
    variables:
      - flowId: ${sign_flowId}
    api: api/mobile-shield/signflows_search_signfields.yml
    extract:
      - sign_sign_field_id: content.data.signfields.0.signfieldId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]


- test:
    name: 签署原流程
    variables:
      - json: { "env": "${env}", "flowId": "${sign_flowId}","group": "${group}" }
    api: api/utils/demosdk/sign/start.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.success",True ]

#- test:
#    name: 签署
#    variables:
#      - params: env=${env}&flow_id=${sign_flowId}&group=${group}
#    api: api/utils/mcp/signing/one_click_sign.yml
#    teardown_hooks:
#      - ${hook_sleep_n_secs(3)}

- test:
    name: 流程完成后，v3验签-未签署的文件返回该文件中未发现签章
    api: api/sign-flow/verify.yml
    variables:
      - fileId: "${fileId2}"
      - json: { "signFlowId": "${sign_flowId}" }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435511 ]
      - eq: [ "content.message","该文件中未发现签章" ]

- test:
    name: 流程完成后，v1验签-未签署的文件返回空数组
    api: api/iterate_cases/documentsVerify.yml
    variables:
      - fileId: "${fileId2}"
      - flowId: "${sign_flowId}"
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - len_eq: [ "content.data.signInfos",0 ]

- test:
    name: 流程完成后，v3验签-已签署的文件返回签名信息
    api: api/sign-flow/verify.yml
    variables:
      - fileId: "${fileId1}"
      - json: { "signFlowId": "${sign_flowId}" }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - len_eq: [ "content.data.signInfos",1 ]
      - len_gt: [ "content.data.signInfos.0.cert.certBase64", 1 ]

- test:
    name: 流程完成后，v1验签已签署的文件返回签名信息
    api: api/iterate_cases/documentsVerify.yml
    variables:
      - fileId: "${fileId1}"
      - flowId: "${sign_flowId}"
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - len_eq: [ "content.data.signInfos",1 ]
